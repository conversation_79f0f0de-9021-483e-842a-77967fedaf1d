// Dashboard functionality
class Dashboard {
    constructor() {
        this.charts = {};
        this.init();
    }
    
    async init() {
        // Check authentication
        if (!Auth.isAuthenticated()) {
            window.location.href = 'login.html';
            return;
        }
        
        // Load dashboard data
        await this.loadSummaryData();
        await this.loadChartData();
        await this.loadRecentActivity();
    }
    
    async loadSummaryData() {
        try {
            const result = await API.dashboard.getSummary();
            
            if (result.success) {
                this.updateSummaryCards(result.data.summary);
            } else {
                Utils.showToast('Failed to load summary data', 'error');
            }
        } catch (error) {
            console.error('Error loading summary data:', error);
            Utils.showToast('Error loading dashboard data', 'error');
        }
    }
    
    updateSummaryCards(summary) {
        // Update total users
        const totalUsersElement = document.getElementById('totalUsers');
        if (totalUsersElement) {
            totalUsersElement.textContent = summary.totalUsers.toLocaleString();
        }
        
        // Update active projects
        const activeProjectsElement = document.getElementById('activeProjects');
        if (activeProjectsElement) {
            activeProjectsElement.textContent = summary.activeProjects.toLocaleString();
        }
        
        // Update pending requests
        const pendingRequestsElement = document.getElementById('pendingRequests');
        if (pendingRequestsElement) {
            pendingRequestsElement.textContent = summary.pendingRequests.toLocaleString();
        }
        
        // Update monthly revenue
        const monthlyRevenueElement = document.getElementById('monthlyRevenue');
        if (monthlyRevenueElement) {
            monthlyRevenueElement.textContent = Utils.formatCurrency(summary.monthlyRevenue);
        }
    }
    
    async loadChartData() {
        try {
            const result = await API.dashboard.getSummary();
            
            if (result.success) {
                this.createRevenueChart(result.data.monthlyStats);
                this.createStatusChart(result.data);
            }
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    }
    
    createRevenueChart(monthlyStats) {
        const ctx = document.getElementById('revenueChart');
        if (!ctx) return;
        
        const labels = monthlyStats.map(stat => 
            Utils.formatDate(stat.month, { month: 'short', year: 'numeric' })
        );
        const data = monthlyStats.map(stat => stat.revenue);
        
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue',
                    data: data,
                    borderColor: CONFIG.CHART_COLORS[0],
                    backgroundColor: CONFIG.CHART_COLORS[0] + '20',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return Utils.formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }
    
    createStatusChart(data) {
        const ctx = document.getElementById('statusChart');
        if (!ctx) return;
        
        // Get project status distribution from recent projects
        const statusCounts = {};
        
        // Initialize with all possible statuses
        Object.keys(CONFIG.PROJECT_STATUSES).forEach(status => {
            statusCounts[status] = 0;
        });
        
        // Count statuses from active projects
        if (data.activeProjects) {
            data.activeProjects.forEach(project => {
                if (statusCounts.hasOwnProperty(project.status)) {
                    statusCounts[project.status]++;
                }
            });
        }
        
        const labels = Object.keys(statusCounts).map(status => 
            CONFIG.PROJECT_STATUSES[status]?.label || status
        );
        const chartData = Object.values(statusCounts);
        
        this.charts.status = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: chartData,
                    backgroundColor: CONFIG.CHART_COLORS.slice(0, labels.length)
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    async loadRecentActivity() {
        try {
            const result = await API.dashboard.getSummary();
            
            if (result.success) {
                this.updateRecentRequests(result.data.recentRequests);
                this.updateActiveProjects(result.data.activeProjects);
            }
        } catch (error) {
            console.error('Error loading recent activity:', error);
        }
    }
    
    updateRecentRequests(requests) {
        const container = document.getElementById('recentRequests');
        if (!container) return;
        
        if (!requests || requests.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-sm">No recent requests</p>';
            return;
        }
        
        container.innerHTML = requests.map(request => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900">${request.title}</h4>
                    <p class="text-xs text-gray-500">
                        ${request.client?.firstName} ${request.client?.lastName} • 
                        ${Utils.formatRelativeTime(request.createdAt)}
                    </p>
                </div>
                <div class="flex-shrink-0">
                    ${Utils.getStatusBadge(request.status, 'request')}
                </div>
            </div>
        `).join('');
    }
    
    updateActiveProjects(projects) {
        const container = document.getElementById('activeProjectsList');
        if (!container) return;
        
        if (!projects || projects.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-sm">No active projects</p>';
            return;
        }
        
        container.innerHTML = projects.map(project => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900">${project.name}</h4>
                    <p class="text-xs text-gray-500">
                        ${project.translator ? `${project.translator.firstName} ${project.translator.lastName}` : 'Unassigned'} • 
                        Due: ${project.dueDate ? Utils.formatDate(project.dueDate) : 'No deadline'}
                    </p>
                </div>
                <div class="flex-shrink-0 flex items-center space-x-2">
                    <div class="text-xs text-gray-500">${project.progress}%</div>
                    ${Utils.getStatusBadge(project.status, 'project')}
                </div>
            </div>
        `).join('');
    }
    
    // Refresh dashboard data
    async refresh() {
        await this.loadSummaryData();
        await this.loadChartData();
        await this.loadRecentActivity();
        Utils.showToast('Dashboard refreshed', 'success');
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (Auth.isAuthenticated()) {
        window.dashboard = new Dashboard();
        
        // Set up refresh functionality if needed
        const refreshButton = document.getElementById('refreshButton');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                window.dashboard.refresh();
            });
        }
    }
});
