// Configuration settings for the frontend application
const CONFIG = {
    // API Base URL - change this to match your backend server
    API_BASE_URL: 'http://localhost:3000/api',
    
    // Application settings
    APP_NAME: 'GlobalLingua Admin',
    VERSION: '1.0.0',
    
    // Authentication settings
    TOKEN_KEY: 'gl_admin_token',
    USER_KEY: 'gl_admin_user',
    
    // Pagination defaults
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
    
    // File upload settings
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    
    // UI settings
    TOAST_DURATION: 5000,
    DEBOUNCE_DELAY: 300,
    
    // Status mappings
    REQUEST_STATUSES: {
        'pending': { label: 'Pending', color: 'yellow' },
        'quoted': { label: 'Quoted', color: 'blue' },
        'approved': { label: 'Approved', color: 'green' },
        'in_progress': { label: 'In Progress', color: 'blue' },
        'review': { label: 'Review', color: 'purple' },
        'completed': { label: 'Completed', color: 'green' },
        'cancelled': { label: 'Cancelled', color: 'red' },
        'rejected': { label: 'Rejected', color: 'red' }
    },
    
    PROJECT_STATUSES: {
        'not_started': { label: 'Not Started', color: 'gray' },
        'in_progress': { label: 'In Progress', color: 'blue' },
        'translation_complete': { label: 'Translation Complete', color: 'green' },
        'review': { label: 'Review', color: 'purple' },
        'qa': { label: 'QA', color: 'orange' },
        'client_review': { label: 'Client Review', color: 'yellow' },
        'completed': { label: 'Completed', color: 'green' },
        'on_hold': { label: 'On Hold', color: 'yellow' },
        'cancelled': { label: 'Cancelled', color: 'red' }
    },
    
    USER_ROLES: {
        'admin': { label: 'Administrator', color: 'red' },
        'manager': { label: 'Manager', color: 'purple' },
        'translator': { label: 'Translator', color: 'blue' },
        'qa': { label: 'QA Specialist', color: 'green' },
        'client': { label: 'Client', color: 'gray' }
    },
    
    USER_STATUSES: {
        'active': { label: 'Active', color: 'green' },
        'inactive': { label: 'Inactive', color: 'gray' },
        'blocked': { label: 'Blocked', color: 'red' },
        'pending': { label: 'Pending', color: 'yellow' }
    },
    
    SERVICE_TYPES: {
        'document_translation': { label: 'Document Translation', icon: 'fas fa-file-alt' },
        'localization': { label: 'Localization', icon: 'fas fa-globe' },
        'interpretation': { label: 'Interpretation', icon: 'fas fa-comments' },
        'proofreading': { label: 'Proofreading', icon: 'fas fa-spell-check' },
        'certified_translation': { label: 'Certified Translation', icon: 'fas fa-stamp' },
        'website_localization': { label: 'Website Localization', icon: 'fas fa-desktop' },
        'other': { label: 'Other', icon: 'fas fa-ellipsis-h' }
    },
    
    LANGUAGES: {
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt': 'Portuguese',
        'ru': 'Russian',
        'zh': 'Chinese',
        'ja': 'Japanese',
        'ko': 'Korean',
        'ar': 'Arabic',
        'hi': 'Hindi',
        'uz': 'Uzbek',
        'tr': 'Turkish',
        'nl': 'Dutch',
        'sv': 'Swedish',
        'no': 'Norwegian',
        'da': 'Danish',
        'fi': 'Finnish',
        'pl': 'Polish'
    },
    
    // Chart colors
    CHART_COLORS: [
        '#3B82F6', // blue
        '#10B981', // green
        '#F59E0B', // yellow
        '#EF4444', // red
        '#8B5CF6', // purple
        '#06B6D4', // cyan
        '#F97316', // orange
        '#84CC16', // lime
        '#EC4899', // pink
        '#6B7280'  // gray
    ]
};

// Utility functions
const Utils = {
    // Format currency
    formatCurrency: (amount, currency = 'USD') => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },
    
    // Format date
    formatDate: (date, options = {}) => {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
    },
    
    // Format relative time
    formatRelativeTime: (date) => {
        const now = new Date();
        const diff = now - new Date(date);
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        return 'Just now';
    },
    
    // Get status badge HTML
    getStatusBadge: (status, type = 'request') => {
        const statusMap = type === 'project' ? CONFIG.PROJECT_STATUSES : CONFIG.REQUEST_STATUSES;
        const statusInfo = statusMap[status] || { label: status, color: 'gray' };
        
        const colorClasses = {
            'green': 'bg-green-100 text-green-800',
            'blue': 'bg-blue-100 text-blue-800',
            'yellow': 'bg-yellow-100 text-yellow-800',
            'red': 'bg-red-100 text-red-800',
            'purple': 'bg-purple-100 text-purple-800',
            'orange': 'bg-orange-100 text-orange-800',
            'gray': 'bg-gray-100 text-gray-800'
        };
        
        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[statusInfo.color] || colorClasses.gray}">
            ${statusInfo.label}
        </span>`;
    },
    
    // Get role badge HTML
    getRoleBadge: (role) => {
        const roleInfo = CONFIG.USER_ROLES[role] || { label: role, color: 'gray' };
        
        const colorClasses = {
            'red': 'bg-red-100 text-red-800',
            'purple': 'bg-purple-100 text-purple-800',
            'blue': 'bg-blue-100 text-blue-800',
            'green': 'bg-green-100 text-green-800',
            'gray': 'bg-gray-100 text-gray-800'
        };
        
        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[roleInfo.color] || colorClasses.gray}">
            ${roleInfo.label}
        </span>`;
    },
    
    // Debounce function
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Show toast notification
    showToast: (message, type = 'info') => {
        const toast = document.createElement('div');
        const typeClasses = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-yellow-500',
            'info': 'bg-blue-500'
        };
        
        toast.className = `fixed top-4 right-4 ${typeClasses[type] || typeClasses.info} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // Remove after duration
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, CONFIG.TOAST_DURATION);
    },
    
    // Validate file
    validateFile: (file) => {
        if (file.size > CONFIG.MAX_FILE_SIZE) {
            return { valid: false, error: 'File size exceeds maximum limit' };
        }

        if (!CONFIG.ALLOWED_FILE_TYPES.includes(file.type)) {
            return { valid: false, error: 'File type not allowed' };
        }

        return { valid: true };
    },

    // Sanitize HTML to prevent XSS
    sanitizeHTML: (str) => {
        if (!str) return '';

        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    },

    // Escape HTML entities
    escapeHTML: (str) => {
        if (!str) return '';

        return str
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;');
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, Utils };
}
