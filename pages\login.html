<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - GlobalLingua Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gradient-primary min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white rounded-full mb-4">
                <i class="fas fa-globe text-2xl text-blue-600"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">GlobalLingua</h1>
            <p class="text-blue-100">Admin Panel</p>
        </div>

        <!-- Login Form -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-white mb-2">
                        <i class="fas fa-envelope mr-2"></i>Email Address
                    </label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        required
                        class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent"
                        placeholder="Enter your email"
                    >
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-white mb-2">
                        <i class="fas fa-lock mr-2"></i>Password
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required
                            class="w-full px-4 py-3 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg text-white placeholder-blue-100 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent pr-12"
                            placeholder="Enter your password"
                        >
                        <button
                            type="button"
                            id="togglePassword"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-100 hover:text-white focus:outline-none"
                            aria-label="Toggle password visibility"
                            title="Show/hide password"
                        >
                            <i class="fas fa-eye" id="eyeIcon"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="remember" 
                            name="remember"
                            class="w-4 h-4 text-blue-600 bg-white bg-opacity-20 border-white border-opacity-30 rounded focus:ring-white focus:ring-2"
                        >
                        <span class="ml-2 text-sm text-blue-100">Remember me</span>
                    </label>
                    <a href="#" class="text-sm text-blue-100 hover:text-white underline">
                        Forgot password?
                    </a>
                </div>

                <!-- Error Message -->
                <div id="errorMessage" class="hidden bg-red-500 bg-opacity-20 border border-red-400 text-red-100 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <span id="errorText"></span>
                </div>

                <!-- Success Message -->
                <div id="successMessage" class="hidden bg-green-500 bg-opacity-20 border border-green-400 text-green-100 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span id="successText"></span>
                </div>

                <button 
                    type="submit" 
                    id="loginButton"
                    class="w-full bg-white text-blue-600 font-semibold py-3 px-4 rounded-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition duration-200 flex items-center justify-center"
                >
                    <span id="loginButtonText">Sign In</span>
                    <i id="loginSpinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>
                </button>
            </form>

            <!-- Additional Info -->
            <div class="mt-6 text-center">
                <p class="text-blue-100 text-sm">
                    Need access? Contact your administrator
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8">
            <p class="text-blue-100 text-sm">
                © 2024 GlobalLingua. All rights reserved.
            </p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/config.js"></script>
    <script src="../js/auth.js"></script>
    <script>
        // Initialize login page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if already logged in
            if (Auth.isAuthenticated()) {
                window.location.href = 'dashboard.html';
                return;
            }

            // Password toggle functionality
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                if (type === 'password') {
                    eyeIcon.className = 'fas fa-eye';
                } else {
                    eyeIcon.className = 'fas fa-eye-slash';
                }
            });

            // Login form submission
            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');
            const loginButtonText = document.getElementById('loginButtonText');
            const loginSpinner = document.getElementById('loginSpinner');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            const errorText = document.getElementById('errorText');
            const successText = document.getElementById('successText');

            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                // Hide previous messages
                errorMessage.classList.add('hidden');
                successMessage.classList.add('hidden');

                // Client-side validation
                const formData = new FormData(loginForm);
                const credentials = {
                    email: formData.get('email'),
                    password: formData.get('password')
                };

                // Validate email format
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!credentials.email || !emailRegex.test(credentials.email)) {
                    errorText.textContent = 'Please enter a valid email address';
                    errorMessage.classList.remove('hidden');
                    return;
                }

                // Validate password
                if (!credentials.password || credentials.password.length < 3) {
                    errorText.textContent = 'Password must be at least 3 characters long';
                    errorMessage.classList.remove('hidden');
                    return;
                }

                // Show loading state
                loginButton.disabled = true;
                loginButtonText.textContent = 'Signing In...';
                loginSpinner.classList.remove('hidden');

                try {
                    const result = await Auth.login(credentials);

                    if (result.success) {
                        successText.textContent = 'Login successful! Redirecting...';
                        successMessage.classList.remove('hidden');

                        // Redirect after short delay
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1000);
                    } else {
                        throw new Error(result.message || 'Login failed');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    errorText.textContent = error.message || 'An error occurred during login';
                    errorMessage.classList.remove('hidden');
                } finally {
                    // Reset button state
                    loginButton.disabled = false;
                    loginButtonText.textContent = 'Sign In';
                    loginSpinner.classList.add('hidden');
                }
            });

            // Auto-focus email field
            document.getElementById('email').focus();
        });
    </script>
</body>
</html>
