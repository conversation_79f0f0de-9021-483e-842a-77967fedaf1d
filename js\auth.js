// Authentication module
const Auth = {
    // Get stored token
    getToken: () => {
        return localStorage.getItem(CONFIG.TOKEN_KEY);
    },
    
    // Set token
    setToken: (token) => {
        localStorage.setItem(CONFIG.TOKEN_KEY, token);
    },
    
    // Remove token
    removeToken: () => {
        localStorage.removeItem(CONFIG.TOKEN_KEY);
        localStorage.removeItem(CONFIG.USER_KEY);
    },
    
    // Get stored user
    getUser: () => {
        const userStr = localStorage.getItem(CONFIG.USER_KEY);
        return userStr ? JSON.parse(userStr) : null;
    },
    
    // Set user
    setUser: (user) => {
        localStorage.setItem(CONFIG.USER_KEY, JSON.stringify(user));
    },
    
    // Check if user is authenticated
    isAuthenticated: () => {
        const token = Auth.getToken();
        const user = Auth.getUser();
        return !!(token && user);
    },
    
    // Check if user has required role
    hasRole: (requiredRoles) => {
        const user = Auth.getUser();
        if (!user) return false;
        
        const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
        return roles.includes(user.role);
    },
    
    // Check if user is admin
    isAdmin: () => {
        return Auth.hasRole('admin');
    },
    
    // Check if user is admin or manager
    isAdminOrManager: () => {
        return Auth.hasRole(['admin', 'manager']);
    },
    
    // Login function
    login: async (credentials) => {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(credentials)
            });

            if (!response.ok) {
                if (response.status === 401) {
                    return { success: false, message: 'Invalid email or password' };
                } else if (response.status >= 500) {
                    return { success: false, message: 'Server error. Please try again later.' };
                } else if (response.status === 429) {
                    return { success: false, message: 'Too many login attempts. Please try again later.' };
                }
            }

            const data = await response.json();

            if (response.ok) {
                Auth.setToken(data.token);
                Auth.setUser(data.user);
                return { success: true, user: data.user };
            } else {
                return { success: false, message: data.message || 'Login failed' };
            }
        } catch (error) {
            console.error('Login error:', error);
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                return { success: false, message: 'Unable to connect to server. Please check your internet connection.' };
            }
            return { success: false, message: 'An unexpected error occurred. Please try again.' };
        }
    },
    
    // Logout function
    logout: async () => {
        try {
            const token = Auth.getToken();
            if (token) {
                await fetch(`${CONFIG.API_BASE_URL}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            Auth.removeToken();
            window.location.href = 'login.html';
        }
    },
    
    // Get current user profile
    getProfile: async () => {
        try {
            const token = Auth.getToken();
            if (!token) throw new Error('No token available');
            
            const response = await fetch(`${CONFIG.API_BASE_URL}/auth/profile`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                Auth.setUser(data.user);
                return { success: true, user: data.user };
            } else {
                throw new Error('Failed to fetch profile');
            }
        } catch (error) {
            console.error('Get profile error:', error);
            return { success: false, message: error.message };
        }
    },
    
    // Update profile
    updateProfile: async (profileData) => {
        try {
            const token = Auth.getToken();
            if (!token) throw new Error('No token available');
            
            const response = await fetch(`${CONFIG.API_BASE_URL}/auth/profile`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(profileData)
            });
            
            const data = await response.json();
            
            if (response.ok) {
                Auth.setUser(data.user);
                return { success: true, user: data.user };
            } else {
                return { success: false, message: data.message || 'Update failed' };
            }
        } catch (error) {
            console.error('Update profile error:', error);
            return { success: false, message: 'Network error. Please try again.' };
        }
    },
    
    // Change password
    changePassword: async (passwordData) => {
        try {
            const token = Auth.getToken();
            if (!token) throw new Error('No token available');
            
            const response = await fetch(`${CONFIG.API_BASE_URL}/auth/change-password`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(passwordData)
            });
            
            const data = await response.json();
            
            if (response.ok) {
                return { success: true, message: data.message };
            } else {
                return { success: false, message: data.message || 'Password change failed' };
            }
        } catch (error) {
            console.error('Change password error:', error);
            return { success: false, message: 'Network error. Please try again.' };
        }
    },
    
    // Initialize authentication
    init: () => {
        // Check authentication on page load
        if (!Auth.isAuthenticated() && !window.location.pathname.includes('login.html')) {
            window.location.href = 'login.html';
            return;
        }
        
        // Set up user menu if authenticated
        if (Auth.isAuthenticated()) {
            Auth.setupUserMenu();
        }
    },
    
    // Setup user menu
    setupUserMenu: () => {
        const user = Auth.getUser();
        if (!user) return;
        
        // Update user name in navigation
        const userNameElement = document.getElementById('userName');
        if (userNameElement) {
            userNameElement.textContent = `${user.firstName} ${user.lastName}`;
        }
        
        // Setup user menu toggle
        const userMenuButton = document.getElementById('userMenuButton');
        const userMenu = document.getElementById('userMenu');
        
        if (userMenuButton && userMenu) {
            userMenuButton.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
            
            // Close menu when clicking outside
            document.addEventListener('click', () => {
                userMenu.classList.add('hidden');
            });
        }
        
        // Setup logout button
        const logoutButton = document.getElementById('logoutButton');
        if (logoutButton) {
            logoutButton.addEventListener('click', (e) => {
                e.preventDefault();
                Auth.logout();
            });
        }
    },
    
    // Handle unauthorized responses
    handleUnauthorized: () => {
        Auth.removeToken();
        Utils.showToast('Session expired. Please login again.', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
    }
};

// Initialize authentication when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Auth.init();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Auth;
}
