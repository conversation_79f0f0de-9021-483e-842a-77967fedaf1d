// Comprehensive test script for GlobalLingua Admin Frontend
// Run this in the browser console to test various functionalities

console.log('🧪 Starting GlobalLingua Admin Frontend Tests...');

// Test 1: Configuration and Utilities
function testConfig() {
    console.log('\n📋 Testing Configuration and Utilities...');
    
    try {
        // Test CONFIG object
        console.assert(typeof CONFIG === 'object', 'CONFIG object should exist');
        console.assert(CONFIG.API_BASE_URL === 'http://localhost:3000/api', 'API_BASE_URL should be set');
        console.assert(typeof CONFIG.LANGUAGES === 'object', 'LANGUAGES should be defined');
        
        // Test Utils functions
        console.assert(typeof Utils.formatCurrency === 'function', 'formatCurrency should be a function');
        console.assert(typeof Utils.formatDate === 'function', 'formatDate should be a function');
        console.assert(typeof Utils.escapeHTML === 'function', 'escapeHTML should be a function');
        console.assert(typeof Utils.sanitizeHTML === 'function', 'sanitizeHTML should be a function');
        
        // Test HTML escaping
        const testHTML = '<script>alert("xss")</script>';
        const escaped = Utils.escapeHTML(testHTML);
        console.assert(!escaped.includes('<script>'), 'HTML should be escaped');
        
        // Test currency formatting
        const formatted = Utils.formatCurrency(1234.56);
        console.assert(formatted.includes('$'), 'Currency should include $ symbol');
        
        console.log('✅ Configuration and Utilities tests passed');
        return true;
    } catch (error) {
        console.error('❌ Configuration and Utilities tests failed:', error);
        return false;
    }
}

// Test 2: Authentication Module
function testAuth() {
    console.log('\n🔐 Testing Authentication Module...');
    
    try {
        // Test Auth object exists
        console.assert(typeof Auth === 'object', 'Auth object should exist');
        console.assert(typeof Auth.login === 'function', 'Auth.login should be a function');
        console.assert(typeof Auth.logout === 'function', 'Auth.logout should be a function');
        console.assert(typeof Auth.isAuthenticated === 'function', 'Auth.isAuthenticated should be a function');
        
        // Test token management
        Auth.setToken('test-token');
        console.assert(Auth.getToken() === 'test-token', 'Token should be stored and retrieved');
        
        // Test user management
        const testUser = { id: 1, name: 'Test User', role: 'admin' };
        Auth.setUser(testUser);
        const retrievedUser = Auth.getUser();
        console.assert(retrievedUser.id === testUser.id, 'User should be stored and retrieved');
        
        // Test role checking
        console.assert(Auth.hasRole('admin') === true, 'Should detect admin role');
        console.assert(Auth.isAdmin() === true, 'Should detect admin user');
        
        console.log('✅ Authentication tests passed');
        return true;
    } catch (error) {
        console.error('❌ Authentication tests failed:', error);
        return false;
    }
}

// Test 3: API Module
function testAPI() {
    console.log('\n🌐 Testing API Module...');
    
    try {
        // Test API object exists
        console.assert(typeof API === 'object', 'API object should exist');
        console.assert(typeof API.request === 'function', 'API.request should be a function');
        console.assert(typeof API.get === 'function', 'API.get should be a function');
        console.assert(typeof API.post === 'function', 'API.post should be a function');
        
        // Test API endpoints exist
        console.assert(typeof API.users === 'object', 'API.users should exist');
        console.assert(typeof API.dashboard === 'object', 'API.dashboard should exist');
        console.assert(typeof API.users.getAll === 'function', 'API.users.getAll should be a function');
        
        console.log('✅ API tests passed');
        return true;
    } catch (error) {
        console.error('❌ API tests failed:', error);
        return false;
    }
}

// Test 4: Form Validation
function testFormValidation() {
    console.log('\n📝 Testing Form Validation...');
    
    try {
        // Test email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        console.assert(emailRegex.test('<EMAIL>'), 'Valid email should pass');
        console.assert(!emailRegex.test('invalid-email'), 'Invalid email should fail');
        
        // Test if users manager has validation
        if (typeof window.usersManager !== 'undefined' && window.usersManager.validateUserData) {
            const validData = {
                email: '<EMAIL>',
                firstName: 'John',
                lastName: 'Doe',
                role: 'user',
                password: 'password123'
            };
            
            const validation = window.usersManager.validateUserData(validData);
            console.assert(validation.valid === true, 'Valid user data should pass validation');
            
            const invalidData = {
                email: 'invalid-email',
                firstName: 'J',
                lastName: '',
                role: '',
                password: '123'
            };
            
            const invalidValidation = window.usersManager.validateUserData(invalidData);
            console.assert(invalidValidation.valid === false, 'Invalid user data should fail validation');
        }
        
        console.log('✅ Form validation tests passed');
        return true;
    } catch (error) {
        console.error('❌ Form validation tests failed:', error);
        return false;
    }
}

// Test 5: UI Components
function testUIComponents() {
    console.log('\n🎨 Testing UI Components...');
    
    try {
        // Test mobile menu elements exist
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const sidebar = document.getElementById('sidebar');
        const mobileOverlay = document.getElementById('mobileOverlay');
        
        if (mobileMenuButton) {
            console.assert(mobileMenuButton.tagName === 'BUTTON', 'Mobile menu button should exist');
        }
        
        if (sidebar) {
            console.assert(sidebar.classList.contains('w-64'), 'Sidebar should have correct width class');
        }
        
        // Test user menu
        const userMenuButton = document.getElementById('userMenuButton');
        const userMenu = document.getElementById('userMenu');
        
        if (userMenuButton && userMenu) {
            console.assert(userMenuButton.tagName === 'BUTTON', 'User menu button should exist');
            console.assert(userMenu.classList.contains('hidden'), 'User menu should be hidden by default');
        }
        
        console.log('✅ UI Components tests passed');
        return true;
    } catch (error) {
        console.error('❌ UI Components tests failed:', error);
        return false;
    }
}

// Test 6: Security Features
function testSecurity() {
    console.log('\n🔒 Testing Security Features...');
    
    try {
        // Test HTML escaping
        const maliciousInput = '<script>alert("XSS")</script>';
        const escaped = Utils.escapeHTML(maliciousInput);
        console.assert(!escaped.includes('<script>'), 'Script tags should be escaped');
        console.assert(escaped.includes('&lt;script&gt;'), 'Should contain escaped HTML');
        
        // Test HTML sanitization
        const sanitized = Utils.sanitizeHTML(maliciousInput);
        console.assert(!sanitized.includes('<script>'), 'Script tags should be sanitized');
        
        console.log('✅ Security tests passed');
        return true;
    } catch (error) {
        console.error('❌ Security tests failed:', error);
        return false;
    }
}

// Test 7: Responsive Design
function testResponsiveDesign() {
    console.log('\n📱 Testing Responsive Design...');
    
    try {
        // Test mobile classes exist
        const mobileElements = document.querySelectorAll('.md\\:hidden, .sm\\:block, .lg\\:grid-cols-4');
        console.assert(mobileElements.length > 0, 'Responsive classes should be present');
        
        // Test sidebar responsiveness
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            console.assert(
                sidebar.classList.contains('md:relative') || 
                sidebar.classList.contains('fixed'), 
                'Sidebar should have responsive positioning'
            );
        }
        
        console.log('✅ Responsive design tests passed');
        return true;
    } catch (error) {
        console.error('❌ Responsive design tests failed:', error);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    console.log('🚀 Running comprehensive test suite...\n');
    
    const tests = [
        testConfig,
        testAuth,
        testAPI,
        testFormValidation,
        testUIComponents,
        testSecurity,
        testResponsiveDesign
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const test of tests) {
        try {
            if (test()) {
                passed++;
            } else {
                failed++;
            }
        } catch (error) {
            console.error(`Test failed with error:`, error);
            failed++;
        }
    }
    
    console.log(`\n📊 Test Results:`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 All tests passed! The application is ready for use.');
    } else {
        console.log('\n⚠️ Some tests failed. Please review the issues above.');
    }
    
    return { passed, failed };
}

// Auto-run tests if this script is loaded
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        setTimeout(runAllTests, 1000); // Give time for other scripts to load
    }
}

// Export for manual testing
window.testSuite = {
    runAllTests,
    testConfig,
    testAuth,
    testAPI,
    testFormValidation,
    testUIComponents,
    testSecurity,
    testResponsiveDesign
};
