// Users management functionality
class UsersManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.filters = {
            search: '',
            role: '',
            status: ''
        };
        this.init();
    }
    
    async init() {
        // Check authentication
        if (!Auth.isAuthenticated()) {
            window.location.href = 'login.html';
            return;
        }
        
        // Check permissions
        if (!Auth.isAdminOrManager()) {
            Utils.showToast('Access denied. Insufficient permissions.', 'error');
            window.location.href = 'dashboard.html';
            return;
        }
        
        this.setupEventListeners();
        await this.loadUsers();
    }
    
    setupEventListeners() {
        // Search input
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', Utils.debounce((e) => {
                this.filters.search = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            }, CONFIG.DEBOUNCE_DELAY));
        }
        
        // Role filter
        const roleFilter = document.getElementById('roleFilter');
        if (roleFilter) {
            roleFilter.addEventListener('change', (e) => {
                this.filters.role = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            });
        }
        
        // Status filter
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.currentPage = 1;
                this.loadUsers();
            });
        }
        
        // Clear filters
        const clearFilters = document.getElementById('clearFilters');
        if (clearFilters) {
            clearFilters.addEventListener('click', () => {
                this.clearFilters();
            });
        }
        
        // Add user button
        const addUserButton = document.getElementById('addUserButton');
        if (addUserButton) {
            addUserButton.addEventListener('click', () => {
                this.showUserModal();
            });
        }
        
        // Modal cancel button
        const cancelButton = document.getElementById('cancelButton');
        if (cancelButton) {
            cancelButton.addEventListener('click', () => {
                this.hideUserModal();
            });
        }
        
        // User form submission
        const userForm = document.getElementById('userForm');
        if (userForm) {
            userForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleUserSubmit(e);
            });
        }
        
        // Modal background click
        const userModal = document.getElementById('userModal');
        if (userModal) {
            userModal.addEventListener('click', (e) => {
                if (e.target === userModal) {
                    this.hideUserModal();
                }
            });
        }
    }
    
    async loadUsers() {
        try {
            const params = {
                page: this.currentPage,
                limit: this.pageSize,
                ...this.filters
            };
            
            const result = await API.users.getAll(params);
            
            if (result.success) {
                this.renderUsers(result.data.users);
                this.renderPagination(result.data.pagination);
            } else {
                Utils.showToast('Failed to load users', 'error');
            }
        } catch (error) {
            console.error('Error loading users:', error);
            Utils.showToast('Error loading users', 'error');
        }
    }
    
    renderUsers(users) {
        const tbody = document.getElementById('usersTableBody');
        if (!tbody) return;
        
        if (!users || users.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                        No users found
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = users.map(user => `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <img class="h-10 w-10 rounded-full" 
                                 src="https://ui-avatars.com/api/?name=${encodeURIComponent(user.firstName + ' ' + user.lastName)}&background=3b82f6&color=fff" 
                                 alt="${user.firstName} ${user.lastName}">
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">
                                ${user.firstName} ${user.lastName}
                            </div>
                            <div class="text-sm text-gray-500">${user.email}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${Utils.getRoleBadge(user.role)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${this.getStatusBadge(user.status)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${user.lastLoginAt ? Utils.formatRelativeTime(user.lastLoginAt) : 'Never'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="usersManager.editUser('${user.id}')" 
                                class="text-blue-600 hover:text-blue-900">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="usersManager.toggleUserStatus('${user.id}', '${user.status}')" 
                                class="text-yellow-600 hover:text-yellow-900">
                            <i class="fas fa-${user.status === 'blocked' ? 'unlock' : 'lock'}"></i>
                        </button>
                        ${Auth.isAdmin() ? `
                            <button onclick="usersManager.deleteUser('${user.id}')" 
                                    class="text-red-600 hover:text-red-900">
                                <i class="fas fa-trash"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    getStatusBadge(status) {
        const statusInfo = CONFIG.USER_STATUSES[status] || { label: status, color: 'gray' };
        
        const colorClasses = {
            'green': 'bg-green-100 text-green-800',
            'gray': 'bg-gray-100 text-gray-800',
            'red': 'bg-red-100 text-red-800',
            'yellow': 'bg-yellow-100 text-yellow-800'
        };
        
        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClasses[statusInfo.color] || colorClasses.gray}">
            ${statusInfo.label}
        </span>`;
    }
    
    renderPagination(pagination) {
        const container = document.getElementById('pagination');
        if (!container) return;
        
        const { currentPage, totalPages, totalItems } = pagination;
        
        container.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button ${currentPage <= 1 ? 'disabled' : ''} 
                            onclick="usersManager.goToPage(${currentPage - 1})"
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : ''}">
                        Previous
                    </button>
                    <button ${currentPage >= totalPages ? 'disabled' : ''} 
                            onclick="usersManager.goToPage(${currentPage + 1})"
                            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : ''}">
                        Next
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">${(currentPage - 1) * this.pageSize + 1}</span> to 
                            <span class="font-medium">${Math.min(currentPage * this.pageSize, totalItems)}</span> of 
                            <span class="font-medium">${totalItems}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            ${this.generatePaginationButtons(currentPage, totalPages)}
                        </nav>
                    </div>
                </div>
            </div>
        `;
    }
    
    generatePaginationButtons(currentPage, totalPages) {
        let buttons = '';
        
        // Previous button
        buttons += `
            <button ${currentPage <= 1 ? 'disabled' : ''} 
                    onclick="usersManager.goToPage(${currentPage - 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : ''}">
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                buttons += `
                    <button class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                        ${i}
                    </button>
                `;
            } else if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                buttons += `
                    <button onclick="usersManager.goToPage(${i})"
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        ${i}
                    </button>
                `;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                buttons += `
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                        ...
                    </span>
                `;
            }
        }
        
        // Next button
        buttons += `
            <button ${currentPage >= totalPages ? 'disabled' : ''} 
                    onclick="usersManager.goToPage(${currentPage + 1})"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 ${currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : ''}">
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        
        return buttons;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadUsers();
    }
    
    clearFilters() {
        this.filters = { search: '', role: '', status: '' };
        this.currentPage = 1;
        
        // Reset form inputs
        document.getElementById('searchInput').value = '';
        document.getElementById('roleFilter').value = '';
        document.getElementById('statusFilter').value = '';
        
        this.loadUsers();
    }
    
    showUserModal(user = null) {
        const modal = document.getElementById('userModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('userForm');
        
        if (user) {
            title.textContent = 'Edit User';
            // Populate form with user data
            form.email.value = user.email;
            form.firstName.value = user.firstName;
            form.lastName.value = user.lastName;
            form.role.value = user.role;
            form.password.required = false;
            form.password.placeholder = 'Leave blank to keep current password';
        } else {
            title.textContent = 'Add User';
            form.reset();
            form.password.required = true;
            form.password.placeholder = '';
        }
        
        modal.classList.remove('hidden');
    }
    
    hideUserModal() {
        const modal = document.getElementById('userModal');
        modal.classList.add('hidden');
    }
    
    async handleUserSubmit(e) {
        const form = e.target;
        const formData = new FormData(form);
        const userData = Object.fromEntries(formData.entries());
        
        try {
            const result = await API.users.create(userData);
            
            if (result.success) {
                Utils.showToast('User created successfully', 'success');
                this.hideUserModal();
                this.loadUsers();
            } else {
                Utils.showToast(result.message || 'Failed to create user', 'error');
            }
        } catch (error) {
            console.error('Error creating user:', error);
            Utils.showToast('Error creating user', 'error');
        }
    }
    
    async editUser(userId) {
        try {
            const result = await API.users.getById(userId);
            
            if (result.success) {
                this.showUserModal(result.data.user);
            } else {
                Utils.showToast('Failed to load user details', 'error');
            }
        } catch (error) {
            console.error('Error loading user:', error);
            Utils.showToast('Error loading user details', 'error');
        }
    }
    
    async toggleUserStatus(userId, currentStatus) {
        const newStatus = currentStatus === 'blocked' ? 'active' : 'blocked';
        const action = newStatus === 'blocked' ? 'block' : 'unblock';
        
        if (!confirm(`Are you sure you want to ${action} this user?`)) {
            return;
        }
        
        try {
            const result = await API.users.updateStatus(userId, newStatus);
            
            if (result.success) {
                Utils.showToast(`User ${action}ed successfully`, 'success');
                this.loadUsers();
            } else {
                Utils.showToast(result.message || `Failed to ${action} user`, 'error');
            }
        } catch (error) {
            console.error(`Error ${action}ing user:`, error);
            Utils.showToast(`Error ${action}ing user`, 'error');
        }
    }
    
    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }
        
        try {
            const result = await API.users.delete(userId);
            
            if (result.success) {
                Utils.showToast('User deleted successfully', 'success');
                this.loadUsers();
            } else {
                Utils.showToast(result.message || 'Failed to delete user', 'error');
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            Utils.showToast('Error deleting user', 'error');
        }
    }
}

// Initialize users manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (Auth.isAuthenticated()) {
        window.usersManager = new UsersManager();
    }
});
