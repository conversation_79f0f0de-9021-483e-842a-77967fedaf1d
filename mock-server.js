// Mock backend server for testing GlobalLingua Admin frontend
const http = require('http');
const url = require('url');

const PORT = 3000;

// Mock data
const mockUsers = [
    {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        status: 'active',
        lastLoginAt: new Date().toISOString(),
        createdAt: new Date().toISOString()
    },
    {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Manager',
        lastName: 'User',
        role: 'manager',
        status: 'active',
        lastLoginAt: new Date(Date.now() - 86400000).toISOString(),
        createdAt: new Date().toISOString()
    },
    {
        id: '3',
        email: '<EMAIL>',
        firstName: 'Translator',
        lastName: 'User',
        role: 'translator',
        status: 'active',
        lastLoginAt: new Date(Date.now() - 172800000).toISOString(),
        createdAt: new Date().toISOString()
    }
];

const mockRequests = [
    {
        id: '1',
        title: 'Document Translation - Contract',
        status: 'pending',
        client: { firstName: '<PERSON>', lastName: 'Doe' },
        createdAt: new Date().toISOString()
    },
    {
        id: '2',
        title: 'Website Localization',
        status: 'in_progress',
        client: { firstName: 'Jane', lastName: 'Smith' },
        createdAt: new Date(Date.now() - 86400000).toISOString()
    }
];

const mockProjects = [
    {
        id: '1',
        name: 'E-commerce Website Translation',
        status: 'in_progress',
        progress: 75,
        translator: { firstName: 'Maria', lastName: 'Garcia' },
        dueDate: new Date(Date.now() + 604800000).toISOString()
    },
    {
        id: '2',
        name: 'Legal Document Translation',
        status: 'review',
        progress: 90,
        translator: { firstName: 'Pierre', lastName: 'Dubois' },
        dueDate: new Date(Date.now() + 259200000).toISOString()
    }
];

// Helper functions
function sendJSON(res, data, statusCode = 200) {
    res.writeHead(statusCode, {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end(JSON.stringify(data));
}

function sendError(res, message, statusCode = 400) {
    sendJSON(res, { error: message, message }, statusCode);
}

// Request handler
function handleRequest(req, res) {
    const parsedUrl = url.parse(req.url, true);
    const path = parsedUrl.pathname;
    const method = req.method;

    // Handle CORS preflight
    if (method === 'OPTIONS') {
        res.writeHead(200, {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        });
        res.end();
        return;
    }

    console.log(`${method} ${path}`);

    // Authentication endpoints
    if (path === '/api/auth/login' && method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            try {
                const { email, password } = JSON.parse(body);
                
                // Simple authentication check
                if (email === '<EMAIL>' && password === 'admin123') {
                    const user = mockUsers.find(u => u.email === email);
                    sendJSON(res, {
                        token: 'mock-jwt-token-' + Date.now(),
                        user: user
                    });
                } else {
                    sendError(res, 'Invalid credentials', 401);
                }
            } catch (error) {
                sendError(res, 'Invalid JSON', 400);
            }
        });
        return;
    }

    if (path === '/api/auth/logout' && method === 'POST') {
        sendJSON(res, { message: 'Logged out successfully' });
        return;
    }

    if (path === '/api/auth/profile' && method === 'GET') {
        const user = mockUsers[0]; // Return admin user
        sendJSON(res, { user });
        return;
    }

    // Dashboard endpoints
    if (path === '/api/dashboard/summary' && method === 'GET') {
        const summary = {
            totalUsers: mockUsers.length,
            activeProjects: mockProjects.length,
            pendingRequests: mockRequests.filter(r => r.status === 'pending').length,
            monthlyRevenue: 25000
        };

        const monthlyStats = [
            { month: '2024-01', revenue: 18000 },
            { month: '2024-02', revenue: 22000 },
            { month: '2024-03', revenue: 25000 }
        ];

        sendJSON(res, {
            summary,
            monthlyStats,
            recentRequests: mockRequests.slice(0, 5),
            activeProjects: mockProjects.slice(0, 5)
        });
        return;
    }

    // Users endpoints
    if (path === '/api/users' && method === 'GET') {
        const { page = 1, limit = 10, search = '', role = '', status = '' } = parsedUrl.query;
        
        let filteredUsers = mockUsers;
        
        if (search) {
            filteredUsers = filteredUsers.filter(user => 
                user.firstName.toLowerCase().includes(search.toLowerCase()) ||
                user.lastName.toLowerCase().includes(search.toLowerCase()) ||
                user.email.toLowerCase().includes(search.toLowerCase())
            );
        }
        
        if (role) {
            filteredUsers = filteredUsers.filter(user => user.role === role);
        }
        
        if (status) {
            filteredUsers = filteredUsers.filter(user => user.status === status);
        }

        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

        sendJSON(res, {
            users: paginatedUsers,
            pagination: {
                currentPage: parseInt(page),
                totalPages: Math.ceil(filteredUsers.length / limit),
                totalItems: filteredUsers.length,
                itemsPerPage: parseInt(limit)
            }
        });
        return;
    }

    if (path.startsWith('/api/users/') && method === 'GET') {
        const userId = path.split('/')[3];
        const user = mockUsers.find(u => u.id === userId);
        
        if (user) {
            sendJSON(res, { user });
        } else {
            sendError(res, 'User not found', 404);
        }
        return;
    }

    if (path === '/api/users' && method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            try {
                const userData = JSON.parse(body);
                const newUser = {
                    id: String(mockUsers.length + 1),
                    ...userData,
                    status: 'active',
                    createdAt: new Date().toISOString(),
                    lastLoginAt: null
                };
                mockUsers.push(newUser);
                sendJSON(res, { user: newUser }, 201);
            } catch (error) {
                sendError(res, 'Invalid JSON', 400);
            }
        });
        return;
    }

    // Default 404
    sendError(res, 'Not found', 404);
}

// Create and start server
const server = http.createServer(handleRequest);

server.listen(PORT, () => {
    console.log(`Mock server running on http://localhost:${PORT}`);
    console.log('Available endpoints:');
    console.log('  POST /api/auth/login');
    console.log('  POST /api/auth/logout');
    console.log('  GET  /api/auth/profile');
    console.log('  GET  /api/dashboard/summary');
    console.log('  GET  /api/users');
    console.log('  GET  /api/users/:id');
    console.log('  POST /api/users');
    console.log('\nTest credentials:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123');
});
