// API module for making HTTP requests
const API = {
    // Base request function
    request: async (endpoint, options = {}) => {
        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
        const token = Auth.getToken();

        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };

        const requestOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), 30000); // 30 second timeout
        });

        try {
            const response = await Promise.race([
                fetch(url, requestOptions),
                timeoutPromise
            ]);

            // Handle unauthorized responses
            if (response.status === 401) {
                Auth.handleUnauthorized();
                throw new Error('Unauthorized');
            }

            // Handle rate limiting
            if (response.status === 429) {
                throw new Error('Too many requests. Please try again later.');
            }

            // Handle server errors
            if (response.status >= 500) {
                throw new Error('Server error. Please try again later.');
            }

            const data = await response.json();

            if (response.ok) {
                return { success: true, data };
            } else {
                return {
                    success: false,
                    error: data.error || 'Request failed',
                    message: data.message || 'An error occurred'
                };
            }
        } catch (error) {
            console.error('API request error:', error);

            if (error.message === 'Request timeout') {
                return {
                    success: false,
                    error: 'Timeout Error',
                    message: 'Request timed out. Please check your connection and try again.'
                };
            }

            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                return {
                    success: false,
                    error: 'Network Error',
                    message: 'Unable to connect to server. Please check your internet connection.'
                };
            }

            return {
                success: false,
                error: 'Request Error',
                message: error.message || 'An unexpected error occurred. Please try again.'
            };
        }
    },
    
    // GET request
    get: (endpoint, params = {}) => {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return API.request(url, { method: 'GET' });
    },
    
    // POST request
    post: (endpoint, data = {}) => {
        return API.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    // PUT request
    put: (endpoint, data = {}) => {
        return API.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    // PATCH request
    patch: (endpoint, data = {}) => {
        return API.request(endpoint, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    },
    
    // DELETE request
    delete: (endpoint) => {
        return API.request(endpoint, { method: 'DELETE' });
    },
    
    // Upload file
    upload: async (endpoint, formData) => {
        const token = Auth.getToken();
        const url = `${CONFIG.API_BASE_URL}${endpoint}`;
        
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    ...(token && { 'Authorization': `Bearer ${token}` })
                },
                body: formData
            });
            
            if (response.status === 401) {
                Auth.handleUnauthorized();
                throw new Error('Unauthorized');
            }
            
            const data = await response.json();
            
            if (response.ok) {
                return { success: true, data };
            } else {
                return { 
                    success: false, 
                    error: data.error || 'Upload failed',
                    message: data.message || 'File upload failed'
                };
            }
        } catch (error) {
            console.error('Upload error:', error);
            return { 
                success: false, 
                error: 'Upload Error',
                message: error.message || 'File upload failed'
            };
        }
    },
    
    // Dashboard API
    dashboard: {
        getSummary: () => API.get('/dashboard/summary'),
        getStats: (period = '30d') => API.get('/dashboard/stats', { period })
    },
    
    // Users API
    users: {
        getAll: (params = {}) => API.get('/users', params),
        getById: (id) => API.get(`/users/${id}`),
        create: (userData) => API.post('/users', userData),
        update: (id, userData) => API.put(`/users/${id}`, userData),
        updateStatus: (id, status) => API.patch(`/users/${id}/status`, { status }),
        delete: (id) => API.delete(`/users/${id}`)
    },
    
    // Translation Requests API
    requests: {
        getAll: (params = {}) => API.get('/requests', params),
        getById: (id) => API.get(`/requests/${id}`),
        updateStatus: (id, status, notes) => API.patch(`/requests/${id}/status`, { status, notes }),
        assign: (id, assignment) => API.patch(`/requests/${id}/assign`, assignment),
        update: (id, requestData) => API.put(`/requests/${id}`, requestData)
    },
    
    // Projects API
    projects: {
        getAll: (params = {}) => API.get('/projects', params),
        getById: (id) => API.get(`/projects/${id}`),
        updateStatus: (id, status, progress) => API.patch(`/projects/${id}/status`, { status, progress }),
        update: (id, projectData) => API.put(`/projects/${id}`, projectData)
    },
    
    // Team API
    team: {
        getAll: (params = {}) => API.get('/team', params),
        add: (memberData) => API.post('/team', memberData),
        updateRole: (id, role) => API.patch(`/team/${id}/role`, { role }),
        getWorkload: (id) => API.get(`/team/${id}/workload`)
    },
    
    // Pricing API
    pricing: {
        getAll: (params = {}) => API.get('/pricing', params),
        create: (pricingData) => API.post('/pricing', pricingData),
        update: (id, pricingData) => API.put(`/pricing/${id}`, pricingData),
        delete: (id) => API.delete(`/pricing/${id}`),
        calculate: (calculationData) => API.post('/pricing/calculate', calculationData)
    },
    
    // Reports API
    reports: {
        getUsage: (params = {}) => API.get('/reports/usage', params),
        getFinance: (params = {}) => API.get('/reports/finance', params)
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
